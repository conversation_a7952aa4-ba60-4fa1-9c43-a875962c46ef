@model IEnumerable<Technoloway.Core.Entities.HeroSlide>

@{
    ViewData["Title"] = "Manage Slides";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="admin-page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-images me-2"></i>
                Manage Slides
            </h1>
            <p class="admin-page-subtitle">@ViewBag.HeroSectionTitle - Slideshow Management</p>
        </div>
        <div>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Hero Sections
            </a>
        </div>
    </div>
</div>

@if (Model.Any())
{
    <div class="row g-3">
        @foreach (var slide in Model)
        {
            <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
                <div class="admin-card slide-card h-100">
                    <div class="card-body p-3">
                        <!-- Slide Header -->
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <div class="slide-order">
                                <span class="badge bg-primary">Order: @slide.DisplayOrder</span>
                            </div>
                            <div class="slide-status">
                                @if (slide.IsActive)
                                {
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Active
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-pause me-1"></i>Inactive
                                    </span>
                                }
                            </div>
                        </div>

                        <!-- Media Preview -->
                        <div class="media-preview mb-3">
                            @if (slide.MediaType == "image" && !string.IsNullOrEmpty(slide.ImageUrl))
                            {
                                <div class="image-preview">
                                    <img src="@slide.ImageUrl" alt="@slide.MediaAlt" class="img-fluid rounded" style="max-height: 120px; width: 100%; object-fit: cover;">
                                </div>
                            }
                            else if (slide.MediaType == "video" && !string.IsNullOrEmpty(slide.VideoUrl))
                            {
                                <div class="video-preview">
                                    <div class="video-placeholder bg-dark text-white d-flex align-items-center justify-content-center rounded" style="height: 120px;">
                                        <i class="fas fa-play-circle fa-3x"></i>
                                    </div>
                                    <small class="text-muted">Video: @slide.VideoUrl</small>
                                </div>
                            }
                            else
                            {
                                <div class="no-media bg-light d-flex align-items-center justify-content-center rounded" style="height: 120px;">
                                    <i class="fas fa-image fa-2x text-muted"></i>
                                </div>
                            }
                        </div>

                        <!-- Content Preview -->
                        <div class="content-preview mb-3">
                            <div class="content-text">
                                @Html.Raw(slide.Content.Length > 100 ? slide.Content.Substring(0, 100) + "..." : slide.Content)
                            </div>
                        </div>

                        <!-- Slide Info -->
                        <div class="slide-info mb-3">
                            <div class="row g-2">
                                <div class="col-6">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        Duration: @slide.Duration ms
                                    </small>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">
                                        <i class="fas fa-magic me-1"></i>
                                        @(slide.AnimationType ?? "fade")
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <div class="btn-group w-100" role="group">
                                <button class="btn btn-outline-primary btn-sm" title="Edit Slide">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" title="Preview">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
}
else
{
    <div class="admin-card">
        <div class="card-body">
            <div class="empty-state text-center py-5">
                <div class="empty-state-icon mb-3">
                    <i class="fas fa-images fa-3x text-muted"></i>
                </div>
                <h3 class="empty-state-title">No Slides Found</h3>
                <p class="empty-state-description text-muted mb-4">
                    This hero section doesn't have any slides yet. Add slides to create a dynamic slideshow.
                </p>
                <button class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Add First Slide
                </button>
            </div>
        </div>
    </div>
}

<style>
    .slide-card {
        transition: all 0.3s ease;
        border: 1px solid var(--admin-border);
        border-radius: 12px;
    }

    .slide-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    }

    .media-preview img {
        transition: transform 0.3s ease;
    }

    .slide-card:hover .media-preview img {
        transform: scale(1.05);
    }

    .content-preview {
        background: rgba(248, 249, 250, 0.8);
        padding: 0.75rem;
        border-radius: 8px;
        border-left: 3px solid var(--admin-primary);
    }

    .content-text {
        font-size: 0.875rem;
        line-height: 1.4;
    }

    .action-buttons .btn-group .btn {
        flex: 1;
        border-radius: 0;
    }

    .action-buttons .btn-group .btn:first-child {
        border-radius: 6px 0 0 6px;
    }

    .action-buttons .btn-group .btn:last-child {
        border-radius: 0 6px 6px 0;
    }

    .action-buttons .btn-group .btn:hover {
        z-index: 2;
    }

    .empty-state-icon {
        opacity: 0.5;
    }

    .empty-state-title {
        color: #495057;
        font-weight: 600;
    }

    .empty-state-description {
        max-width: 400px;
        margin: 0 auto;
    }

    @@media (max-width: 768px) {
        .action-buttons .btn-group {
            flex-direction: column;
        }

        .action-buttons .btn-group .btn {
            border-radius: 6px !important;
            margin-bottom: 0.25rem;
        }

        .action-buttons .btn-group .btn:last-child {
            margin-bottom: 0;
        }
    }
</style>
