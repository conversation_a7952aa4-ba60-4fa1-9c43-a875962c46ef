@model IEnumerable<Technoloway.Core.Entities.HeroSection>
@using Technoloway.Core.Entities

@{
    ViewData["Title"] = "Hero Sections";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="admin-page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-image me-2"></i>
                Hero Sections
            </h1>
            <p class="admin-page-subtitle">Manage homepage hero sections and slideshow content</p>
        </div>
        <div>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Create New Hero Section
            </a>
        </div>
    </div>
</div>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@Html.AntiForgeryToken()

@if (Model.Any())
{
    <div class="row g-3">
        @foreach (var item in Model)
        {
            <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
                <div class="admin-card hero-section-card-compact h-100">
                    <div class="card-body p-3">
                        <!-- Header with Title and Status Toggle -->
                        <div class="d-flex align-items-start justify-content-between mb-3">
                            <div class="flex-grow-1 me-2">
                                <h6 class="mb-1 fw-bold text-dark">@item.Title</h6>
                                <small class="text-muted">@item.PageName</small>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input status-toggle" type="checkbox"
                                       @(item.IsActive ? "checked" : "")
                                       data-id="@item.Id"
                                       title="Toggle Active Status">
                            </div>
                        </div>

                        <!-- Main Title Preview -->
                        <div class="main-title-preview mb-3">
                            <div class="fw-medium text-primary small">@Html.Raw(item.MainTitle)</div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="quick-stats mb-3">
                            <div class="row g-2">
                                <div class="col-6">
                                    <button class="btn btn-outline-info btn-sm w-100 slides-btn"
                                            data-id="@item.Id"
                                            title="Manage Slides">
                                        <i class="fas fa-images me-1"></i>
                                        @item.Slides.Count(s => !s.IsDeleted) Slides
                                    </button>
                                </div>
                                <div class="col-6">
                                    <div class="slideshow-status text-center">
                                        @if (item.EnableSlideshow)
                                        {
                                            <span class="badge bg-success small">
                                                <i class="fas fa-play me-1"></i>Auto
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary small">
                                                <i class="fas fa-stop me-1"></i>Manual
                                            </span>
                                        }
                                        <div class="text-muted" style="font-size: 0.7rem;">
                                            @(item.EnableSlideshow ? $"{item.SlideshowSpeed}ms" : "Static")
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Last Modified -->
                        <div class="last-modified mb-3">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                @item.LastModified.ToString("MMM dd, HH:mm")
                            </small>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <div class="btn-group w-100" role="group">
                                <a asp-action="Details" asp-route-id="@item.Id"
                                   class="btn btn-outline-info btn-sm" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a asp-action="Edit" asp-route-id="@item.Id"
                                   class="btn btn-outline-primary btn-sm" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a asp-action="Delete" asp-route-id="@item.Id"
                                   class="btn btn-outline-danger btn-sm" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
}
else
{
    <div class="admin-card">
        <div class="card-body">
            <div class="empty-state text-center py-5">
                <div class="empty-state-icon mb-3">
                    <i class="fas fa-image fa-3x text-muted"></i>
                </div>
                <h3 class="empty-state-title">No Hero Sections Found</h3>
                <p class="empty-state-description text-muted mb-4">
                    Create your first hero section to manage homepage slideshow content and call-to-action elements.
                </p>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Create First Hero Section
                </a>
            </div>
        </div>
    </div>
}

<style>
    .hero-section-card-compact {
        transition: all 0.3s ease;
        border: 1px solid var(--admin-border);
        border-radius: 12px;
        height: 100%;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .hero-section-card-compact:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
        border-color: rgba(79, 70, 229, 0.2);
    }

    .main-title-preview {
        background: rgba(79, 70, 229, 0.05);
        border-left: 3px solid var(--admin-primary);
        padding: 0.5rem;
        border-radius: 0 6px 6px 0;
        margin-left: -0.75rem;
        margin-right: -0.75rem;
    }

    .status-toggle {
        cursor: pointer;
        transform: scale(1.2);
    }

    .status-toggle:checked {
        background-color: var(--admin-success);
        border-color: var(--admin-success);
    }

    .slides-btn {
        transition: all 0.2s ease;
        border-radius: 8px;
        font-weight: 500;
        background-color: rgba(13, 202, 240, 0.1) !important;
        border-color: #0dcaf0 !important;
        color: #0dcaf0 !important;
    }

    .slides-btn:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(13, 202, 240, 0.3);
        background-color: #0dcaf0 !important;
        color: #fff !important;
        border-color: #0dcaf0 !important;
    }

    .slides-btn:focus {
        box-shadow: 0 0 0 0.2rem rgba(13, 202, 240, 0.25);
    }

    .slideshow-status .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }

    .last-modified {
        border-top: 1px solid #e9ecef;
        padding-top: 0.5rem;
    }

    .action-buttons .btn-group .btn {
        flex: 1;
        border-radius: 0;
        font-weight: 500;
        transition: all 0.2s ease;
        border-width: 1px;
        position: relative;
    }

    .action-buttons .btn-group .btn:first-child {
        border-radius: 6px 0 0 6px;
    }

    .action-buttons .btn-group .btn:last-child {
        border-radius: 0 6px 6px 0;
    }

    .action-buttons .btn-group .btn:hover {
        z-index: 2;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    /* Enhanced button styles for better visibility */
    .action-buttons .btn-outline-info {
        color: #0dcaf0;
        border-color: #0dcaf0;
        background-color: rgba(13, 202, 240, 0.1);
    }

    .action-buttons .btn-outline-info:hover {
        color: #fff;
        background-color: #0dcaf0;
        border-color: #0dcaf0;
    }

    .action-buttons .btn-outline-primary {
        color: #0d6efd;
        border-color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.1);
    }

    .action-buttons .btn-outline-primary:hover {
        color: #fff;
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .action-buttons .btn-outline-danger {
        color: #dc3545;
        border-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.1);
    }

    .action-buttons .btn-outline-danger:hover {
        color: #fff;
        background-color: #dc3545;
        border-color: #dc3545;
    }

    /* Icon styling for better visibility */
    .action-buttons .btn i {
        font-size: 0.875rem;
        font-weight: 600;
    }

    /* Card content styling for better readability */
    .hero-section-card-compact .card-body {
        padding: 1rem;
    }

    .hero-section-card-compact h6 {
        color: #2d3748;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .hero-section-card-compact small {
        color: #718096;
        font-weight: 500;
    }

    /* Status toggle enhanced styling */
    .form-check-input:checked {
        background-color: #10b981 !important;
        border-color: #10b981 !important;
    }

    .form-check-input:focus {
        border-color: #10b981;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
    }

    /* Badge styling improvements */
    .slideshow-status .badge {
        font-weight: 600;
        letter-spacing: 0.025em;
    }

    /* Hover state for entire card */
    .hero-section-card-compact:hover .main-title-preview {
        background: rgba(79, 70, 229, 0.08);
        border-left-color: var(--admin-primary);
    }

    .hero-section-card-compact:hover .slides-btn {
        transform: scale(1.05);
    }

    .empty-state-icon {
        opacity: 0.5;
    }

    .empty-state-title {
        color: #495057;
        font-weight: 600;
    }

    .empty-state-description {
        max-width: 400px;
        margin: 0 auto;
    }

    /* Loading state for status toggle */
    .status-toggle.loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Success animation */
    .status-toggle.success {
        animation: successPulse 0.6s ease-in-out;
    }

    @@keyframes successPulse {
        0% { transform: scale(1.2); }
        50% { transform: scale(1.4); }
        100% { transform: scale(1.2); }
    }

    @@media (max-width: 768px) {
        .col-xl-4 {
            margin-bottom: 1rem;
        }

        .action-buttons .btn-group {
            flex-direction: column;
        }

        .action-buttons .btn-group .btn {
            border-radius: 6px !important;
            margin-bottom: 0.25rem;
        }

        .action-buttons .btn-group .btn:last-child {
            margin-bottom: 0;
        }
    }
</style>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Status toggle functionality
            $('.status-toggle').on('change', function() {
                const toggle = $(this);
                const heroSectionId = toggle.data('id');
                const isChecked = toggle.is(':checked');

                // Add loading state
                toggle.addClass('loading');

                $.ajax({
                    url: '@Url.Action("ToggleActive", "HeroSections")',
                    type: 'POST',
                    data: { id: heroSectionId },
                    headers: {
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update toggle state to match server response
                            toggle.prop('checked', response.isActive);

                            // Add success animation
                            toggle.addClass('success');
                            setTimeout(() => toggle.removeClass('success'), 600);

                            // Show success message
                            showToast('success', `Hero section ${response.isActive ? 'activated' : 'deactivated'} successfully`);
                        } else {
                            // Revert toggle state on error
                            toggle.prop('checked', !isChecked);
                            showToast('error', response.message || 'Error updating status');
                        }
                    },
                    error: function() {
                        // Revert toggle state on error
                        toggle.prop('checked', !isChecked);
                        showToast('error', 'Error updating hero section status');
                    },
                    complete: function() {
                        // Remove loading state
                        toggle.removeClass('loading');
                    }
                });
            });

            // Slides button functionality
            $('.slides-btn').on('click', function() {
                const heroSectionId = $(this).data('id');
                window.location.href = '@Url.Action("Slides", "HeroSections")/' + heroSectionId;
            });
        });

        // Toast notification function
        function showToast(type, message) {
            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            // Create toast container if it doesn't exist
            if (!$('#toast-container').length) {
                $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>');
            }

            const $toast = $(toastHtml);
            $('#toast-container').append($toast);

            const toast = new bootstrap.Toast($toast[0]);
            toast.show();

            // Remove toast element after it's hidden
            $toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
}
